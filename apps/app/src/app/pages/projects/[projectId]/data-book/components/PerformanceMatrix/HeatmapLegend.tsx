import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import { getHeatmapColorClasses, HEATMAP_LEVELS, HEATMAP_THEME } from './heatmap-config';

export const HeatmapLegend = () => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard.healthLevels');

  return (
    <div className="overflow-x-auto">
      <div className="flex flex-row justify-end place-items-end p-1 pr-2 min-w-max">
        {HEATMAP_LEVELS.slice(1).map((level) => {
          const badgeClasses = getHeatmapColorClasses(HEATMAP_THEME, level, 'badge');
          return (
            <div key={level} className="p-2">
              <Badge label={messages(`${level}.label`)} className={badgeClasses} />
            </div>
          );
        })}
      </div>
    </div>
  );
};
