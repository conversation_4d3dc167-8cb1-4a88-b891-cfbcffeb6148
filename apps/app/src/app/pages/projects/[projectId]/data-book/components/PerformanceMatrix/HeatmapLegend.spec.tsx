import React from 'react';
import { render, screen } from 'tests/test-utils';
import { HeatmapLegend } from './HeatmapLegend';

describe('<HeatmapLegend />', () => {
  it('renders the heatmap legend', () => {
    render(<HeatmapLegend />);

    expect(
      screen.getByRole('status', { name: /dataBook.page.heatmapDashboard.healthLevels.1.label/ })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('status', { name: /dataBook.page.heatmapDashboard.healthLevels.2.label/ })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('status', { name: /dataBook.page.heatmapDashboard.healthLevels.3.label/ })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('status', { name: /dataBook.page.heatmapDashboard.healthLevels.4.label/ })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('status', { name: /dataBook.page.heatmapDashboard.healthLevels.5.label/ })
    ).toBeInTheDocument();
  });
});
