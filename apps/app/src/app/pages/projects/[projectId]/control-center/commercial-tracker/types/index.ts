import type {
  ChangeSignalDowntimeDetailsBasicSchema,
  ChangeSignalIssueDetailsBasicSchema,
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  PotentialChangeSchema,
  potentialChangeEstimatedCostImpactEnum,
  potentialChangeEstimatedScheduleImpactEnum,
  potentialChangePriorityEnum,
  potentialChangeStatusEnum,
} from '@shape-construction/api/src/types';
import { potentialChangeCategoryEnum } from '@shape-construction/api/src/types';
import type { THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';

export type FieldProps = {
  record: PotentialChangeSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
  potentialChangeId?: PotentialChangeSchema['id'];
};

interface PriorityOption {
  label: string;
  theme?: THEME;
  icon?: React.ReactElement;
}

interface CategoryOption {
  label: string;
  theme?: THEME;
}

interface StatusOption {
  label: string;
  theme?: THEME;
}

interface EstimatedCostImpactOption {
  label: string;
  theme?: THEME;
}
interface EstimatedScheduleImpactOption {
  label: string;
  theme?: THEME;
}

export const selectablePotentialChangeCategoryEnum = {
  internal: potentialChangeCategoryEnum.internal,
  client_issues: potentialChangeCategoryEnum.client_issues,
  undefined: potentialChangeCategoryEnum.undefined,
  supplier: potentialChangeCategoryEnum.supplier,
} as const;

export type PriorityOptions = {
  [K in keyof typeof potentialChangePriorityEnum]: PriorityOption;
};

export type CategoryOptions = {
  [K in keyof typeof selectablePotentialChangeCategoryEnum]: CategoryOption;
};

export type StatusOptions = {
  [K in keyof typeof potentialChangeStatusEnum]: StatusOption;
};

export type EstimatedCostImpactOptions = {
  [K in keyof typeof potentialChangeEstimatedCostImpactEnum]: EstimatedCostImpactOption;
};

export type EstimatedScheduleImpactOptions = {
  [K in keyof typeof potentialChangeEstimatedScheduleImpactEnum]: EstimatedScheduleImpactOption;
};

export type PotentialChangeSignalsStep = 'VIEW' | 'LINK';
export type ChangeSignalsUnselect = 'SINGLE' | 'ALL';

export type ChangeSignalListItem = ChangeSignalIssueDetailsBasicSchema | ChangeSignalDowntimeDetailsBasicSchema;
