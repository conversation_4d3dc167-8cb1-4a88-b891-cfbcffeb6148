import { changeSignalsIssuesFactory, potentialChangeFactory } from '@shape-construction/api/factories/control-center';
import { render, screen } from 'tests/test-utils';
import { PotentialChangeSignalsList } from './PotentialChangeSignalsList';

describe('<PotentialChangeSignalsList />', () => {
  describe('when there are change signals', () => {
    it('renders the list of change signals', async () => {
      const changeSignal = changeSignalsIssuesFactory();
      const potentialChange = potentialChangeFactory({ signals: [changeSignal], signalsCount: 1 });

      render(
        <PotentialChangeSignalsList
          onUnlinkChangeSignalClick={() => {}}
          onLinkChangeSignalsClick={jest.fn()}
          potentialChange={potentialChange}
          isUnlinkingChangeSignals
        />
      );

      expect(await screen.findByRole('list')).toBeInTheDocument();
    });

    it('renders the correct number of change signals', async () => {
      const signals = [changeSignalsIssuesFactory(), changeSignalsIssuesFactory(), changeSignalsIssuesFactory()];
      const potentialChange = potentialChangeFactory({ signals, signalsCount: 3 });

      render(
        <PotentialChangeSignalsList
          onUnlinkChangeSignalClick={() => {}}
          onLinkChangeSignalsClick={jest.fn()}
          potentialChange={potentialChange}
          isUnlinkingChangeSignals
        />
      );

      expect(await screen.findAllByRole('listitem')).toHaveLength(3);
    });
  });

  describe('when there are no change signals', () => {
    it('renders the empty state message', async () => {
      const potentialChange = potentialChangeFactory({ signalsCount: 0 });

      render(
        <PotentialChangeSignalsList
          onUnlinkChangeSignalClick={() => {}}
          onLinkChangeSignalsClick={jest.fn()}
          potentialChange={potentialChange}
          isUnlinkingChangeSignals
        />
      );

      expect(
        await screen.findByText(
          'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.emptyState.title'
        )
      ).toBeInTheDocument();
      expect(
        screen.getByText('controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.emptyState.subtitle')
      ).toBeInTheDocument();
    });
  });

  describe('when the user can link signals', () => {
    it('renders the link signals button', async () => {
      const potentialChange = potentialChangeFactory();
      potentialChange.availableActions.linkChangeSignals = true;

      render(
        <PotentialChangeSignalsList
          onUnlinkChangeSignalClick={() => {}}
          onLinkChangeSignalsClick={jest.fn()}
          potentialChange={potentialChange}
          isUnlinkingChangeSignals
        />
      );

      expect(
        await screen.findByRole('button', {
          name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.linkSignals',
        })
      ).toBeInTheDocument();
    });
  });

  describe('when the user cannot link signals', () => {
    it('does not render the link signals button', () => {
      const potentialChange = potentialChangeFactory();
      potentialChange.availableActions.linkChangeSignals = false;

      render(
        <PotentialChangeSignalsList
          onUnlinkChangeSignalClick={() => {}}
          onLinkChangeSignalsClick={jest.fn()}
          potentialChange={potentialChange}
          isUnlinkingChangeSignals
        />
      );

      expect(
        screen.queryByRole('button', {
          name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.linkSignals',
        })
      ).not.toBeInTheDocument();
    });
  });
});
