import { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type {
  potentialChangeEstimatedCostImpactEnum,
  potentialChangeEstimatedScheduleImpactEnum,
  potentialChangePriorityEnum,
  potentialChangeStatusEnum,
} from '@shape-construction/api/src/types';
import { THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import { ChartBarIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { sentenceCase } from 'change-case';
import type {
  CategoryOptions,
  EstimatedCostImpactOptions,
  EstimatedScheduleImpactOptions,
  PriorityOptions,
  StatusOptions,
  selectablePotentialChangeCategoryEnum,
} from '../types';

/**
 * Custom hook that provides field options for priority and status
 * @returns An object containing field options organized by key
 */
const useFieldOptions = () => {
  const messages = useMessageGetter('controlCenter.commercialTracker.options');

  const PRIORITY_OPTIONS = useMemo<PriorityOptions>(
    () => ({
      low: {
        label: messages('priority.low'),
        theme: THEME.GREEN_DARK,
        icon: <ChartBarIcon aria-label="priority" height={16} className={'text-gray-600'} />,
      },
      medium: {
        label: messages('priority.medium'),
        theme: THEME.GREEN_DARK,
        icon: <ChartBarIcon aria-label="priority" height={16} className={'text-yellow-600'} />,
      },
      high: {
        label: messages('priority.high'),
        theme: THEME.GREEN_DARK,
        icon: <ChartBarIcon aria-label="priority" height={16} className={'text-red-600'} />,
      },
    }),
    [messages]
  );

  const CATEGORY_OPTIONS = useMemo<CategoryOptions>(
    () => ({
      client_issues: {
        label: messages('category.clientIssues'),
      },
      internal: {
        label: messages('category.internal'),
      },
      undefined: {
        label: messages('category.undefined'),
      },
      supplier: {
        label: messages('category.supplier'),
      },
    }),
    [messages]
  );

  const STATUS_OPTIONS = useMemo<StatusOptions>(
    () => ({
      approved_by_client: {
        label: messages('status.approvedByClient'),
        theme: THEME.GREEN_DARK,
      },
      awaiting_client_review: {
        label: messages('status.awaitingClientReview'),
        theme: THEME.YELLOW,
      },
      canceled: {
        label: messages('status.canceled'),
        theme: THEME.PINK_DARK,
      },
      client_clarification_requested: {
        label: messages('status.clientClarificationRequested'),
        theme: THEME.YELLOW_ALT,
      },
      instruction_and_implementation: {
        label: messages('status.instructionAndImplementation'),
        theme: THEME.GREEN,
      },
      rejected_by_client: {
        label: messages('status.rejectedByClient'),
        theme: THEME.RED,
      },
      submitted: {
        label: messages('status.submitted'),
        theme: THEME.BLUE,
      },
      under_internal_review: {
        label: messages('status.underInternalReview'),
        theme: THEME.TEAL,
      },
      under_preparation: {
        label: messages('status.underPreparation'),
        theme: THEME.GRAY,
      },
      unprocessed: {
        label: messages('status.unprocessed'),
        theme: THEME.WHITE,
      },
    }),
    [messages]
  );

  const ESTIMATED_COST_IMPACT_OPTIONS = useMemo<EstimatedCostImpactOptions>(
    () => ({
      extensive: {
        label: messages('estimatedCostImpact.extensive'),
        theme: THEME.RED,
      },
      major: {
        label: messages('estimatedCostImpact.major'),
        theme: THEME.BLUE,
      },
      minor: {
        label: messages('estimatedCostImpact.minor'),
        theme: THEME.YELLOW,
      },
      moderate: {
        label: messages('estimatedCostImpact.moderate'),
        theme: THEME.GREEN,
      },
      none: {
        label: messages('estimatedCostImpact.none'),
        theme: THEME.GRAY,
      },
      significant: {
        label: messages('estimatedCostImpact.significant'),
        theme: THEME.PINK,
      },
    }),
    [messages]
  );

  const ESTIMATED_SCHEDULE_IMPACT_OPTIONS = useMemo<EstimatedScheduleImpactOptions>(
    () => ({
      extended_duration: {
        label: messages('estimatedScheduleImpact.extendedDuration'),
        theme: THEME.RED,
      },
      long_term: {
        label: messages('estimatedScheduleImpact.longTerm'),
        theme: THEME.BLUE,
      },
      medium_term: {
        label: messages('estimatedScheduleImpact.mediumTerm'),
        theme: THEME.YELLOW,
      },
      minimal: {
        label: messages('estimatedScheduleImpact.minimal'),
        theme: THEME.GREEN,
      },
      none: {
        label: messages('estimatedScheduleImpact.none'),
        theme: THEME.GRAY,
      },
      short_term: {
        label: messages('estimatedScheduleImpact.shortTerm'),
        theme: THEME.PINK,
      },
    }),
    [messages]
  );

  const getCategoryLabel = (category: keyof typeof selectablePotentialChangeCategoryEnum) =>
    CATEGORY_OPTIONS[category]?.label || sentenceCase(category);

  const getEstimatedCostImpactLabel = (costImpact: keyof typeof potentialChangeEstimatedCostImpactEnum) =>
    ESTIMATED_COST_IMPACT_OPTIONS[costImpact]?.label || sentenceCase(costImpact);

  const getEstimatedScheduleImpactLabel = (scheduleImpact: keyof typeof potentialChangeEstimatedScheduleImpactEnum) =>
    ESTIMATED_SCHEDULE_IMPACT_OPTIONS[scheduleImpact]?.label || sentenceCase(scheduleImpact);

  const getPriorityIcon = (priority: keyof typeof potentialChangePriorityEnum) =>
    PRIORITY_OPTIONS[priority]?.icon || PRIORITY_OPTIONS.low.icon;
  const getPriorityLabel = (priority: keyof typeof potentialChangePriorityEnum) =>
    PRIORITY_OPTIONS[priority]?.label || sentenceCase(priority);

  const getStatusLabel = (status: keyof typeof potentialChangeStatusEnum) =>
    STATUS_OPTIONS[status]?.label || sentenceCase(status);
  const getStatusTheme = (status: keyof typeof potentialChangeStatusEnum) =>
    STATUS_OPTIONS[status]?.theme || THEME.GRAY;

  return {
    getCategoryLabel,
    getEstimatedCostImpactLabel,
    getEstimatedScheduleImpactLabel,
    getPriorityIcon,
    getPriorityLabel,
    getStatusLabel,
    getStatusTheme,
  };
};

export { useFieldOptions };
