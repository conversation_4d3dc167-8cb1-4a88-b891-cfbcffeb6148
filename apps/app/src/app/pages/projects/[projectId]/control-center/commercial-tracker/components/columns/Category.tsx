import { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import {
  type PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema,
  type PotentialChangeDetailsBasicSchema,
  potentialChangeCategoryEnum,
} from '@shape-construction/api/src/types';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { type CategorySelectOption, useCategoryGroupedOptions } from '../../hooks/useCategoryOptions';

type CategoryProps = {
  record: PotentialChangeDetailsBasicSchema;
  onUpdatePotentialChangeRecord: (
    values: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema
  ) => void;
};

export const Category: React.FC<CategoryProps> = ({ record, onUpdatePotentialChangeRecord }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker');
  const { categoryGroupedOptions, categoryUngroupedOptions } = useCategoryGroupedOptions();

  const value = useMemo(() => {
    if (record.category === potentialChangeCategoryEnum.organisation) {
      return categoryUngroupedOptions.find((option) => option.teamId === record.responsibleTeamId);
    }
    return categoryUngroupedOptions.find((option) => option.category === record.category);
  }, [categoryUngroupedOptions, record.category, record.responsibleTeamId]);

  const handleChange = (value: CategorySelectOption) => {
    if (value.category !== record.category || value.teamId !== record.responsibleTeamId) {
      onUpdatePotentialChangeRecord({
        category: value.category,
        responsible_team_id: value.teamId,
      });
    }
  };

  return (
    <Select.Root onChange={handleChange} value={value} className="border-none w-40">
      <Select.Trigger
        className="bg-transparent h-6 group hover:ring-1 hover:ring-gray-400 hover:text-gray-700 hover:bg-gray-50"
        size="sm"
        aria-label={messages('fields.category')}
        variant="plain"
        showChevronOnHover
      >
        {record.category ? (
          <span className="text-xs leading-4 font-normal text-neutral-bold">{value?.name}</span>
        ) : (
          <span className="text-xs leading-4 font-normal text-neutral-subtlest">{messages('select')}</span>
        )}
      </Select.Trigger>

      <Select.ResponsivePanel className="border-none" portal>
        <Select.Options>
          {categoryGroupedOptions.map(({ group, options }, index) => (
            <div key={group ?? index}>
              {group && (
                <p className="px-3 pt-3 pb-2 text-xs font-semibold uppercase leading-4 tracking-wider text-gray-400">
                  {group}
                </p>
              )}
              {options.map((option) => (
                <Select.Option key={option.key} value={option}>
                  <Select.OptionText className="text-xs">{option.name}</Select.OptionText>
                </Select.Option>
              ))}
            </div>
          ))}
        </Select.Options>
      </Select.ResponsivePanel>
    </Select.Root>
  );
};
