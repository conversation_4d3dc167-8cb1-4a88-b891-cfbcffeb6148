import React from 'react';
import { potentialChangeFactory } from '@shape-construction/api/factories/control-center';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { teamFactory } from '@shape-construction/api/factories/teams';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdTeamsMockHandler } from '@shape-construction/api/handlers-factories/projects/teams';
import { potentialChangeCategoryEnum } from '@shape-construction/api/src/types';
import { createMemoryHistory } from 'history';
import { server } from 'tests/mock-server';
import { render, screen, waitFor } from 'tests/test-utils';
import { Category } from './Category';

describe('<Category />', () => {
  it('shows placeholder text', async () => {
    const project = projectFactory();
    const teams = [teamFactory({ id: 'team-1', displayName: 'Construction Corp' })];
    const potentialChange = potentialChangeFactory();
    const mockOnUpdatePotentialChangeRecord = jest.fn();
    const history = createMemoryHistory({
      initialEntries: [`/projects/${project.id}/control-center`],
    });
    const route = { path: `/projects/${project.id}/control-center` };
    server.use(
      getApiProjectsProjectIdMockHandler(() => project),
      getApiProjectsProjectIdTeamsMockHandler(() => teams)
    );

    render(
      <Category
        record={{ ...potentialChange, category: null }}
        onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
      />,
      { route, history }
    );

    await waitFor(() => {
      expect(screen.getByText('controlCenter.commercialTracker.select')).toBeInTheDocument();
    });
  });

  describe('when category is selected', () => {
    it('displays category label', async () => {
      const project = projectFactory();
      const teams = [teamFactory({ id: 'team-1', displayName: 'Construction Corp' })];
      const potentialChange = potentialChangeFactory();
      const mockOnUpdatePotentialChangeRecord = jest.fn();
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/control-center`],
      });
      const route = { path: `/projects/${project.id}/control-center` };
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdTeamsMockHandler(() => teams)
      );

      render(
        <Category
          record={{ ...potentialChange, category: 'internal' }}
          onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
        />,
        { route, history }
      );

      await waitFor(() => {
        expect(screen.getByText('controlCenter.commercialTracker.options.category.internal')).toBeInTheDocument();
      });
    });
  });

  describe('when dropdown is clicked', () => {
    it('displays category options', async () => {
      const project = projectFactory();
      const teams = [teamFactory({ id: 'team-1', displayName: 'Construction Corp' })];
      const potentialChange = potentialChangeFactory();
      const mockOnUpdatePotentialChangeRecord = jest.fn();
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/control-center`],
      });
      const route = { path: `/projects/${project.id}/control-center` };
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdTeamsMockHandler(() => teams)
      );
      const { user } = render(
        <Category
          record={{ ...potentialChange, category: null }}
          onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
        />,
        { route, history }
      );

      await user.click(await screen.findByRole('button', { name: 'controlCenter.commercialTracker.fields.category' }));

      expect(screen.getByText('controlCenter.commercialTracker.options.category.clientIssues')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.options.category.internal')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.options.category.undefined')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.options.category.supplier')).toBeInTheDocument();
    });

    it('displays organisation options', async () => {
      const project = projectFactory();
      const teams = [teamFactory({ id: 'team-1', displayName: 'Construction Corp' })];
      const potentialChange = potentialChangeFactory();
      const mockOnUpdatePotentialChangeRecord = jest.fn();
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/control-center`],
      });
      const route = { path: `/projects/${project.id}/control-center` };
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdTeamsMockHandler(() => teams)
      );
      const { user } = render(
        <Category
          record={{ ...potentialChange, category: null }}
          onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
        />,
        { route, history }
      );

      await user.click(await screen.findByRole('button', { name: 'controlCenter.commercialTracker.fields.category' }));

      expect(
        screen.getByText('controlCenter.commercialTracker.options.category.group.organisations')
      ).toBeInTheDocument();
      expect(screen.getByText('Construction Corp')).toBeInTheDocument();
    });
  });

  describe('when category option is selected', () => {
    it('calls onUpdatePotentialChangeRecord', async () => {
      const project = projectFactory();
      const teams = [teamFactory({ id: 'team-1', displayName: 'Construction Corp' })];
      const potentialChange = potentialChangeFactory();
      const mockOnUpdatePotentialChangeRecord = jest.fn();
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/control-center`],
      });
      const route = { path: `/projects/${project.id}/control-center` };
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdTeamsMockHandler(() => teams)
      );
      const { user } = render(
        <Category
          record={{ ...potentialChange, category: null }}
          onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
        />,
        { route, history }
      );

      await user.click(await screen.findByRole('button', { name: 'controlCenter.commercialTracker.fields.category' }));
      await user.click(
        await screen.findByRole('option', { name: 'controlCenter.commercialTracker.options.category.internal' })
      );

      expect(mockOnUpdatePotentialChangeRecord).toHaveBeenCalledWith({
        category: 'internal',
        responsible_team_id: null,
      });
    });
  });

  describe('when organisation option is selected', () => {
    it('calls onUpdatePotentialChangeRecord', async () => {
      const project = projectFactory();
      const teams = [teamFactory({ id: 'team-1', displayName: 'Construction Corp' })];
      const potentialChange = potentialChangeFactory();
      const mockOnUpdatePotentialChangeRecord = jest.fn();
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/control-center`],
      });
      const route = { path: `/projects/${project.id}/control-center` };
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdTeamsMockHandler(() => teams)
      );
      const { user } = render(
        <Category
          record={{ ...potentialChange, category: potentialChangeCategoryEnum.organisation }}
          onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
        />,
        { route, history }
      );

      await user.click(await screen.findByRole('button', { name: 'controlCenter.commercialTracker.fields.category' }));
      await user.click(await screen.findByRole('option', { name: 'Construction Corp' }));

      expect(mockOnUpdatePotentialChangeRecord).toHaveBeenCalledWith({
        category: potentialChangeCategoryEnum.organisation,
        responsible_team_id: 'team-1',
      });
    });
  });

  describe('when organisation category is selected', () => {
    it('displays organisation name', async () => {
      const project = projectFactory();
      const teams = [teamFactory({ id: 'team-1', displayName: 'Construction Corp' })];
      const potentialChange = potentialChangeFactory();
      const mockOnUpdatePotentialChangeRecord = jest.fn();
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/control-center`],
      });
      const route = { path: `/projects/${project.id}/control-center` };
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdTeamsMockHandler(() => teams)
      );

      render(
        <Category
          record={{
            ...potentialChange,
            category: potentialChangeCategoryEnum.organisation,
            responsibleTeamId: 'team-1',
          }}
          onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
        />,
        { route, history }
      );

      await waitFor(() => {
        expect(screen.getByText('Construction Corp')).toBeInTheDocument();
      });
    });
  });

  describe('when same option is selected again', () => {
    it('does not call onUpdatePotentialChangeRecord', async () => {
      const project = projectFactory();
      const teams = [teamFactory({ id: 'team-1', displayName: 'Construction Corp' })];
      const potentialChange = potentialChangeFactory();
      const mockOnUpdatePotentialChangeRecord = jest.fn();
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/control-center`],
      });
      const route = { path: `/projects/${project.id}/control-center` };
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdTeamsMockHandler(() => teams)
      );
      const { user } = render(
        <Category
          record={{
            ...potentialChange,
            category: 'internal',
            responsibleTeamId: null,
          }}
          onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
        />,
        { route, history }
      );

      await user.click(await screen.findByRole('button', { name: 'controlCenter.commercialTracker.fields.category' }));
      await user.click(
        await screen.findByRole('option', { name: 'controlCenter.commercialTracker.options.category.internal' })
      );

      expect(mockOnUpdatePotentialChangeRecord).not.toHaveBeenCalled();
    });
  });

  describe('when organisation with same teamId is selected again', () => {
    it('does not call onUpdatePotentialChangeRecord', async () => {
      const project = projectFactory();
      const teams = [teamFactory({ id: 'team-1', displayName: 'Construction Corp' })];
      const potentialChange = potentialChangeFactory();
      const mockOnUpdatePotentialChangeRecord = jest.fn();
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/control-center`],
      });
      const route = { path: `/projects/${project.id}/control-center` };
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdTeamsMockHandler(() => teams)
      );
      const { user } = render(
        <Category
          record={{
            ...potentialChange,
            category: potentialChangeCategoryEnum.organisation,
            responsibleTeamId: 'team-1',
          }}
          onUpdatePotentialChangeRecord={mockOnUpdatePotentialChangeRecord}
        />,
        { route, history }
      );

      await user.click(await screen.findByRole('button', { name: 'controlCenter.commercialTracker.fields.category' }));
      await user.click(await screen.findByRole('option', { name: 'Construction Corp' }));

      expect(mockOnUpdatePotentialChangeRecord).not.toHaveBeenCalled();
    });
  });
});
