import { useMemo } from 'react';
import { useMessage } from '@messageformat/react';
import {
  type PotentialChangeCategoryEnumSchema,
  potentialChangeCategoryEnum,
  type TeamSchema,
} from '@shape-construction/api/src/types';
import { useSuspenseQuery } from '@tanstack/react-query';
import { useCurrentProject } from 'app/contexts/currentProject';
import { getProjectTeamsQueryOptions } from 'app/queries/projects/teams';
import { selectablePotentialChangeCategoryEnum } from '../types';
import { useFieldOptions } from './useFieldOptions';

export type CategorySelectOption = {
  key: string;
  name: string;
  category: PotentialChangeCategoryEnumSchema;
  teamId: TeamSchema['id'] | null;
};

export const useCategoryGroupedOptions = () => {
  const organisationsMessage = useMessage('controlCenter.commercialTracker.options.category.group.organisations');
  const { getCategoryLabel } = useFieldOptions();
  const project = useCurrentProject();
  const { data: teams } = useSuspenseQuery(getProjectTeamsQueryOptions(project.id));

  const categoryOptions: CategorySelectOption[] = useMemo(
    () =>
      Object.values(selectablePotentialChangeCategoryEnum).map((category) => ({
        key: category,
        category,
        name: getCategoryLabel(category),
        teamId: null,
      })),
    [getCategoryLabel]
  );

  const organisationOptions: CategorySelectOption[] = useMemo(
    () =>
      teams.map((team) => ({
        key: team.id,
        category: potentialChangeCategoryEnum.organisation,
        name: team.displayName ?? '',
        teamId: team.id,
      })),
    [teams]
  );

  const categoryGroupedOptions = useMemo(() => {
    return [
      {
        options: categoryOptions,
      },
      {
        group: organisationsMessage,
        options: organisationOptions,
      },
    ];
  }, [categoryOptions, organisationOptions, organisationsMessage]);

  const categoryUngroupedOptions = useMemo(
    () => categoryGroupedOptions.flatMap(({ options }) => options),
    [categoryGroupedOptions]
  );

  return {
    categoryGroupedOptions,
    categoryUngroupedOptions,
  };
};
