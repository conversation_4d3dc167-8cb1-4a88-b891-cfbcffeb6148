import { projectFactory } from '@shape-construction/api/factories/projects';
import { teamFactory } from '@shape-construction/api/factories/teams';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdTeamsMockHandler } from '@shape-construction/api/handlers-factories/projects/teams';
import { potentialChangeCategoryEnum } from '@shape-construction/api/src/types';
import { waitFor } from '@testing-library/react';
import { createMemoryHistory } from 'history';
import { server } from 'tests/mock-server';
import { renderHook } from 'tests/test-utils';
import { useCategoryGroupedOptions } from './useCategoryOptions';

describe('useCategoryOptions', () => {
  it('returns category and organisation grouped options', async () => {
    const project = projectFactory();
    const teams = [
      teamFactory({ id: 'team-1', displayName: 'Construction Corp' }),
      teamFactory({ id: 'team-2', displayName: 'Engineering Ltd' }),
    ];
    const history = createMemoryHistory({
      initialEntries: [`/projects/${project.id}/control-center`],
    });
    const route = { path: `/projects/${project.id}/control-center` };
    server.use(
      getApiProjectsProjectIdMockHandler(() => project),
      getApiProjectsProjectIdTeamsMockHandler(() => teams)
    );

    const { result } = renderHook(() => useCategoryGroupedOptions(), { route, history });

    await waitFor(() => {
      expect(result.current.categoryGroupedOptions).toHaveLength(2);
    });
    const { categoryGroupedOptions, categoryUngroupedOptions } = result.current;
    // First group - standard categories
    expect(categoryGroupedOptions[0].group).toBeUndefined();
    expect(categoryGroupedOptions[0].options).toHaveLength(4);
    expect(categoryGroupedOptions[0].options[0]).toMatchObject({
      key: 'internal',
      category: 'internal',
      teamId: null,
    });
    // Second group - organisations
    expect(categoryGroupedOptions[1].group).toBe(
      'controlCenter.commercialTracker.options.category.group.organisations'
    );
    expect(categoryGroupedOptions[1].options).toHaveLength(2);
    expect(categoryGroupedOptions[1].options[0]).toMatchObject({
      key: 'team-1',
      category: potentialChangeCategoryEnum.organisation,
      name: 'Construction Corp',
      teamId: 'team-1',
    });
    // Ungrouped options combine both groups
    expect(categoryUngroupedOptions).toHaveLength(6);
  });
});
