import { faker } from '@faker-js/faker';
import type { ChangeSignalDowntimeSchema, ChangeSignalIssueSchema, PotentialChangeSchema } from '../src/types';
import { documentFactory } from './documents';
import { issueFactory } from './issues';
import { shiftReportsDownTimeFactory, shiftReportsFactory } from './shiftReports';
import type { Factory } from './utils';

export const potentialChangeFactory: Factory<PotentialChangeSchema> = (props): PotentialChangeSchema => {
  return {
    archived: false,
    availableActions: potentialChangeAvailableActionsFactory(),
    category: 'client_issues',
    signals: [],
    signalsCount: 0,
    comment: 'comment-test',
    cost: '',
    createdAt: new Date().toISOString(),
    earlyWarningNoticeSubmitted: true,
    estimatedCostImpact: 'major',
    estimatedScheduleImpact: 'long_term',
    id: faker.string.uuid(),
    priority: 'high',
    responsibleTeamId: null,
    status: 'unprocessed',
    teamMemberId: faker.number.int(),
    title: 'Default title',
    workingDaysOfDelay: 0,
    ...props,
  };
};

export const changeSignalsIssuesFactory: Factory<ChangeSignalIssueSchema> = (props): ChangeSignalIssueSchema => {
  return {
    title: 'issue change signal',
    impact: 'completedDelay',
    locationId: 'location-0',
    publishedAt: new Date().toISOString(),
    id: faker.string.uuid(),
    signalType: 'issue',
    teamMemberId: faker.number.int(),
    documents: [documentFactory()],
    issue: issueFactory(),
    ...props,
  };
};

export const changeSignalsDowntimesFactory: Factory<ChangeSignalDowntimeSchema> = (
  props
): ChangeSignalDowntimeSchema => {
  return {
    title: 'issue change signal',
    locationId: 'location-0',
    publishedAt: new Date().toISOString(),
    shiftReportId: faker.string.uuid(),
    id: faker.string.uuid(),
    signalType: 'downtime',
    teamMemberId: faker.number.int(),
    documents: [documentFactory()],
    issue: null,
    downtime: shiftReportsDownTimeFactory(),
    shiftReport: shiftReportsFactory(),
    ...props,
  };
};

export const potentialChangeAvailableActionsFactory: Factory<PotentialChangeSchema['availableActions']> = (
  props
): PotentialChangeSchema['availableActions'] => {
  return {
    archive: true,
    linkChangeSignals: true,
    unlinkChangeSignals: true,
    ...props,
  };
};
