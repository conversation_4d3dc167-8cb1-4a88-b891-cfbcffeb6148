/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { PotentialChangeCategorySchema } from './potentialChangeCategorySchema';
import type { PotentialChangeEstimatedCostImpactSchema } from './potentialChangeEstimatedCostImpactSchema';
import type { PotentialChangeEstimatedScheduleImpactSchema } from './potentialChangeEstimatedScheduleImpactSchema';
import type { PotentialChangePrioritySchema } from './potentialChangePrioritySchema';
import type { PotentialChangeSchema } from './potentialChangeSchema';
import type { PotentialChangeStatusSchema } from './potentialChangeStatusSchema';

export type PostApiProjectsProjectIdControlCenterPotentialChangesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Potential change created
 */
export type PostApiProjectsProjectIdControlCenterPotentialChanges201Schema = PotentialChangeSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdControlCenterPotentialChanges401Schema = AuthenticationErrorSchema;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdControlCenterPotentialChanges404Schema = void;

/**
 * @description Invalid request
 */
export type PostApiProjectsProjectIdControlCenterPotentialChanges422Schema = ErrorSchema;

export type PostApiProjectsProjectIdControlCenterPotentialChangesMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  category?: PotentialChangeCategorySchema;
  /**
   * @type string | undefined
   */
  comment?: string;
  /**
   * @type string | undefined
   */
  cost?: string;
  /**
   * @type boolean | undefined
   */
  early_warning_notice_submitted?: boolean;
  /**
   * @type string | undefined
   */
  estimated_cost_impact?: PotentialChangeEstimatedCostImpactSchema;
  /**
   * @type string | undefined
   */
  estimated_schedule_impact?: PotentialChangeEstimatedScheduleImpactSchema;
  /**
   * @type string | undefined
   */
  priority?: PotentialChangePrioritySchema;
  /**
   * @type string | undefined, uuid
   */
  responsible_team_id?: string;
  /**
   * @type string | undefined
   */
  status?: PotentialChangeStatusSchema;
  /**
   * @type string | undefined
   */
  title?: string;
  /**
   * @type number | undefined, float
   */
  working_days_of_delay?: number;
};

export type PostApiProjectsProjectIdControlCenterPotentialChangesMutationResponseSchema =
  PostApiProjectsProjectIdControlCenterPotentialChanges201Schema;

export type PostApiProjectsProjectIdControlCenterPotentialChangesSchemaMutation = {
  Response: PostApiProjectsProjectIdControlCenterPotentialChanges201Schema;
  Request: PostApiProjectsProjectIdControlCenterPotentialChangesMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdControlCenterPotentialChangesPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdControlCenterPotentialChanges401Schema
    | PostApiProjectsProjectIdControlCenterPotentialChanges404Schema
    | PostApiProjectsProjectIdControlCenterPotentialChanges422Schema;
};
