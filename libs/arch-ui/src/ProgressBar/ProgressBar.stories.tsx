import React from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import {
  type ProgressBarActiveColor,
  ProgressBarBadge,
  ProgressBarBadgeTheme,
  ProgressBarHeader,
  type ProgressBarProps,
  ProgressBarRoot,
  type ProgressBarSize,
  ProgressBarSubtitle,
  ProgressBarTitle,
} from './ProgressBar';

const progressBarColors: ProgressBarActiveColor[] = ['primary', 'success', 'warning', 'danger', 'secondary'];
const progressBarSizes: ProgressBarSize[] = ['small', 'medium', 'large'];

const meta: Meta<typeof ProgressBarRoot> = {
  title: 'Status/ProgressBar',
  component: ProgressBarRoot,
};
export default meta;
type Story = StoryObj<ProgressBarProps>;

export const Default: Story = {
  args: {
    progress: 50,
    size: 'medium',
  },
  render: (args) => <ProgressBarRoot {...args} />,
};

export const Colors: Story = {
  args: {
    progress: 50,
    size: 'medium',
  },
  render: (args) => (
    <div className="flex flex-col gap-4">
      {progressBarColors.map((color, index) => (
        <div key={color} className="flex flex-col gap-1">
          <span className="text-white">{color}</span>
          <ProgressBarRoot {...args} color={color} progress={index * 10 + 20} />

          <div className="flex flex-col gap-1">
            <span className="text-white">{color} - disabled</span>
            <ProgressBarRoot {...args} color={color} progress={index * 10 + 20} disabled />
          </div>
        </div>
      ))}
    </div>
  ),
};

export const Sizes: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <div className="flex flex-col gap-4">
      {progressBarSizes.map((size, index) => (
        <div key={size} className="flex flex-col gap-1">
          <span className="text-white">{size}</span>
          <ProgressBarRoot {...args} size={size} progress={index * 10 + 20} />
        </div>
      ))}
    </div>
  ),
};

export const WithPercentage: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <div className="flex flex-col gap-4">
      {progressBarSizes.map((size, index) => (
        <div key={size} className="flex flex-col gap-1">
          <ProgressBarRoot {...args} size={size} progress={index * 10 + 20}>
            <ProgressBarHeader showProgress>
              <ProgressBarTitle>{size}</ProgressBarTitle>
            </ProgressBarHeader>
          </ProgressBarRoot>
        </div>
      ))}
    </div>
  ),
};

export const WithTitleAndSubtitle: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>Title</ProgressBarTitle>
        <ProgressBarSubtitle>Subtitle</ProgressBarSubtitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};

export const WithTitle: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>Title</ProgressBarTitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};

export const WithBadge: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>
          Subtitle
          <ProgressBarBadge label="title" theme={ProgressBarBadgeTheme.BLUE} />
        </ProgressBarTitle>
        <ProgressBarSubtitle>
          Subtitle
          <ProgressBarBadge label="subtitle" theme={ProgressBarBadgeTheme.GREEN} />
        </ProgressBarSubtitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};
export const DarkMode: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <div data-mode="dark" className="bg-gray-800 p-10">
      <ProgressBarRoot {...args}>
        <ProgressBarHeader showProgress>
          <ProgressBarTitle>Title</ProgressBarTitle>
          <ProgressBarSubtitle>Subtitle</ProgressBarSubtitle>
        </ProgressBarHeader>
      </ProgressBarRoot>
    </div>
  ),
};
export const Disabled: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <ProgressBarRoot {...args} disabled>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>Title</ProgressBarTitle>
        <ProgressBarSubtitle>Subtitle</ProgressBarSubtitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};
