import React from 'react';
import { render, screen } from '@testing-library/react';
import {
  ProgressBarBadge,
  ProgressBarBadgeTheme,
  ProgressBarHeader,
  ProgressBarRoot,
  ProgressBarSubtitle,
  ProgressBarTitle,
} from './ProgressBar';

describe('ProgressBar', () => {
  it('renders correctly', () => {
    render(<ProgressBarRoot progress={50} />);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByRole('progressbar')).toHaveAttribute('aria-valuenow', '50');
  });

  describe('when the title and subtitle is defined', () => {
    it('renders with the title and subtitle', () => {
      render(
        <ProgressBarRoot progress={40}>
          <ProgressBarHeader>
            <ProgressBarTitle>Progress</ProgressBarTitle>
            <ProgressBarSubtitle>Progress subtitle</ProgressBarSubtitle>
          </ProgressBarHeader>
        </ProgressBarRoot>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByText('Progress')).toBeInTheDocument();
      expect(screen.getByText('Progress subtitle')).toBeInTheDocument();
    });
  });

  describe('when the showProgress is true on the header', () => {
    it('renders the progress value', () => {
      render(
        <ProgressBarRoot progress={40}>
          <ProgressBarHeader showProgress />
        </ProgressBarRoot>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByText('40%')).toBeInTheDocument();
    });
  });

  describe('when a badge is defined', () => {
    it('renders the badge', () => {
      render(
        <ProgressBarRoot progress={40}>
          <ProgressBarHeader>
            <ProgressBarTitle>
              Example
              <ProgressBarBadge theme={ProgressBarBadgeTheme.BLUE} label="This is my badge" />
            </ProgressBarTitle>
            <ProgressBarSubtitle>
              Example
              <ProgressBarBadge theme={ProgressBarBadgeTheme.BLUE} label="This is my other badge" />
            </ProgressBarSubtitle>
          </ProgressBarHeader>
        </ProgressBarRoot>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByText('This is my badge')).toBeInTheDocument();
      expect(screen.getByText('This is my other badge')).toBeInTheDocument();
    });
  });

  describe('when disabled', () => {
    it('renders as disabled', () => {
      render(<ProgressBarRoot progress={40} disabled />);

      expect(screen.getByRole('progressbar')).toHaveAttribute('aria-disabled', 'true');
    });
  });
});
